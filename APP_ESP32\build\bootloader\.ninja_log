# ninja log v6
46	2482	7756341267376005	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	f83e44f21f901685
371	2732	7756341270622417	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b5e87001bc69fec4
233	2953	7756341269240697	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	c2e49dae8332393a
528	3053	7756341272198835	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	c682aee1daebbb5e
620	3300	7756341273113094	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	dd6239af6335d820
787	3441	7756341274791365	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	75983807d0c6550a
890	3777	7756341275815094	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7f3483c8f075d0a
1075	3928	7756341277667577	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	2bc42ed5747bc341
1296	4133	7756341279874972	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	887ad8966bf87f20
1595	4309	7756341282868335	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9869316161f1e42a
2487	4686	7756341291786133	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	e5d10a1737688e3
2733	4978	7756341294242571	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7d185c20b1fdc6cf
3301	5190	7756341299928051	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	ba83749ffc209caf
2954	5625	7756341296460071	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	60a559932a62cb97
3055	5778	7756341297466385	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	5e5d6cab9b7f4366
3779	6388	7756341304703848	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a899fb33ddbd2df4
4134	6494	7756341308250520	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e73e0cbf31510e2c
3929	6794	7756341306202657	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	eedb715fc52d1998
4310	7113	7756341310011121	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bf8515f08e6b30b1
4687	7426	7756341313783394	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	61ebb76299390e73
3442	7830	7756341301334498	esp-idf/log/liblog.a	e54b20c7fa2964d7
5190	9506	7756341318804334	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	fd28e15f7791601
4979	9645	7756341316704306	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	5b443093f80d2fcd
5779	9761	7756341324698096	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c0bf79a33a3a966c
5626	9872	7756341323174003	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	48ea26fbb7353a17
6389	10042	7756341330808547	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	4bfe0e7c38df55ad
6495	10144	7756341331863348	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	a3d1c232ec156460
7114	10392	7756341338052031	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	b17df1134d02787
6794	10643	7756341334861371	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	bffb6e4346a6f891
7427	10776	7756341341187547	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1298ec82f28650dd
9646	11990	7756341363377301	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	984f45d1bdd858ed
9507	12093	7756341361987734	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	b43ecd807a7fe6e1
9761	12531	7756341364541647	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2e673cb66c8c557
9872	12752	7756341365638786	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4965e824ce0d69f6
10042	13115	7756341367342253	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ae97c3779b25d814
10145	13361	7756341368356430	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	61f45e6509e1486a
7831	13723	7756341345220677	esp-idf/esp_rom/libesp_rom.a	805d24816b3a60c9
10392	13930	7756341370842407	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	143c0cb5c4772aaa
10776	14105	7756341374679185	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e1a9586b680c3c5f
10644	14249	7756341373343931	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	eaa1d9f30df50085
11990	14571	7756341386816413	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2361422639f63810
12093	15328	7756341387843362	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a463245cca6390ec
12531	15495	7756341392231836	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	ae6c203d51eef8b1
12752	15599	7756341394441166	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	71b9743d9ed5da87
13116	15786	7756341398070149	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	a30498979c3f4366
13362	16076	7756341400532492	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	9df60cefedf39ea3
14250	17484	7756341409405015	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	7ad56293ddb9a5d6
13930	17773	7756341406221527	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	2a6da2106c2a3108
14106	17886	7756341407976121	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	6f918bcff2a8774d
16076	18190	7756341427672285	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2b591bacdc03593f
15787	18383	7756341424782786	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	5c7c7ccbdeb9e007
15328	18562	7756341420194226	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	a32df1f5c33d9256
14572	18634	7756341412631821	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1a9c882ae89fc2b4
15600	18753	7756341422895795	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	8d4a4faddd03c48d
15495	18968	7756341421864727	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	e87f461648b0b1f6
17773	20232	7756341444652510	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	d94e33c62e2bcda0
17484	20482	7756341441762295	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	62b3cb3af83bf7c4
17887	20661	7756341445768644	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	479687e421f1e72d
13723	21152	7756341404143741	esp-idf/esp_common/libesp_common.a	b71236a9bcb917c9
18384	21308	7756341450757877	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fc54b7091d361441
18191	21591	7756341448828636	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	269406c78723102f
18754	21683	7756341454430529	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	58c9cc4c7ebc503a
18635	21836	7756341453262173	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	da612cb17c158e8f
18562	21952	7756341452536326	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	44d2ddf6416cfa03
18969	22100	7756341456595866	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	eaf5bc897b3f041a
20233	22824	7756341469248894	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9590d63968d6166
20661	23138	7756341473531467	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	72e5bc03abc51642
20483	23666	7756341471747053	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	19587ff2692ded53
21308	23889	7756341480000544	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a88c4ecbca3c5fe5
21836	24133	7756341485281243	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	e17d90a7cd874f0e
21952	24386	7756341486421515	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	e2764f1ff992994b
21683	24587	7756341483753378	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	75404385d42b11f4
22101	25085	7756341487928325	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	999b773e11e29e28
21592	25489	7756341482836716	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	347040af540d0376
23138	25876	7756341498293733	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	7dd7c130e614c583
22825	25986	7756341495165409	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	35a22860f0b4bf73
24133	26196	7756341508249985	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b09220aabc2aef7d
23666	26396	7756341503582192	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a0809e503ef67c7f
23889	26594	7756341505790401	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	571566a91022656b
21152	27016	7756341478442967	esp-idf/esp_hw_support/libesp_hw_support.a	b3e17a15a8dc9385
24587	27304	7756341512775221	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	314c349a4af25f75
24387	27726	7756341510782728	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	faa4e2a4eb9342e0
25086	28206	7756341517779029	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a38a4ab26e1046f2
25877	28704	7756341525687425	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	371b7ab80dd4abab
26197	28985	7756341528887224	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	5cbb9b9c4ca71c65
25490	29201	7756341521817223	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	76bb0d5d8f7c150a
25986	29317	7756341526779536	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	c789c90c18fa154d
26594	29513	7756341532859284	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	3452635f0fabe6a8
26397	29768	7756341530878154	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	d5e7d1b185545051
27304	30316	7756341539961097	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	d012e1b6cecb7fab
27727	30719	7756341544186494	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	75d5ddb1643d2728
28206	31213	7756341548978672	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd38888be186abab
28705	31685	7756341553966878	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	ed7148953d48bc5d
29201	31923	7756341558927513	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	40035d70a4af072f
28986	31981	7756341556776810	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	19a739fcab5aa2f5
29514	32565	7756341562053817	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	cd459680db3b1a8
27017	32752	7756341537084108	esp-idf/esp_system/libesp_system.a	24bb0c2fe6a0c356
29769	32978	7756341564605426	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	ba52318d24e50e57
29318	32980	7756341560094276	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	b7f14fb0075c618f
30317	33514	7756341570080387	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	1f7cd98b26682029
30719	34015	7756341600646288	project_elf_src_esp32s3.c	fe633a5f4d72cd2d
30719	34015	7756341600646288	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/project_elf_src_esp32s3.c	fe633a5f4d72cd2d
31214	34216	7756341579042186	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	27344fe5b8035443
34015	35047	7756341607062604	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	503356affbd4e6fb
32752	35791	7756341594438040	esp-idf/efuse/libefuse.a	3c1c1105f6217f7f
35791	37728	7756341624822724	esp-idf/bootloader_support/libbootloader_support.a	49c58ff06b01234d
37728	39099	7756341644192653	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	3a9c204458b8a058
39099	40746	7756341657906703	esp-idf/spi_flash/libspi_flash.a	af13735a3c836284
40746	41995	7756341674368242	esp-idf/hal/libhal.a	60eb6124c554fe19
41995	43272	7756341686865619	esp-idf/micro-ecc/libmicro-ecc.a	db7c16ad8cac7493
43272	44713	7756341699541590	esp-idf/soc/libsoc.a	fbb706948c61c9c9
44714	45766	7756341714044376	esp-idf/xtensa/libxtensa.a	18ee135e1db9edc2
45766	46838	7756341724549506	esp-idf/main/libmain.a	a1fa28df99b21283
46838	48420	7756341735249191	bootloader.elf	d4dbe372795bfcb8
48421	50242	7756341765940129	.bin_timestamp	ab181d3961ab39b2
48421	50242	7756341765940129	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/.bin_timestamp	ab181d3961ab39b2
50242	51108	7756341769332571	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
50242	51108	7756341769332571	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
39	879	7756343556617703	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
39	879	7756343556617703	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
